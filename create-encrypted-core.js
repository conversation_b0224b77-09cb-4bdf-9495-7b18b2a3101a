// 创建加密核心代码的脚本
const fs = require('fs');

// 加密工具类
class EnhancedCryptoUtils {
    constructor() {
        this.mixChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=!@#$%^&*()_+-=[]{}|;:,.<>?~`';
        this.salt = 'X-Enhancer-Simba-2024';
        this.expectedPasswordHash = this.generatePasswordHash('simba');
    }

    generatePasswordHash(password) {
        const combined = password + this.salt;
        let hash = 0;
        for (let i = 0; i < combined.length; i++) {
            const char = combined.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16);
    }

    verifyPassword(password) {
        const inputHash = this.generatePasswordHash(password);
        return inputHash === this.expectedPasswordHash;
    }

    generateMixKey(password) {
        const hash = this.generatePasswordHash(password);
        let mixKey = '';
        for (let i = 0; i < hash.length; i++) {
            const charCode = hash.charCodeAt(i);
            mixKey += this.mixChars[charCode % this.mixChars.length];
        }
        return mixKey;
    }

    mixString(base64String, mixKey) {
        let mixed = '';
        let keyIndex = 0;
        
        for (let i = 0; i < base64String.length; i++) {
            mixed += base64String[i];
            
            if ((i + 1) % 3 === 0 && keyIndex < mixKey.length) {
                mixed += mixKey[keyIndex % mixKey.length];
                keyIndex++;
            }
        }
        
        const extraMixCount = mixKey.length % 5 + 2;
        for (let i = 0; i < extraMixCount; i++) {
            mixed += mixKey[i % mixKey.length];
        }
        
        return mixed;
    }

    calculateChecksum(data) {
        let checksum = 0;
        for (let i = 0; i < data.length; i++) {
            checksum += data.charCodeAt(i);
        }
        return (checksum * 31 + data.length).toString(16);
    }

    encrypt(data, password) {
        if (!this.verifyPassword(password)) {
            throw new Error('Invalid password');
        }

        try {
            const jsonString = JSON.stringify(data);
            const base64 = Buffer.from(jsonString, 'utf8').toString('base64');
            const mixKey = this.generateMixKey(password);
            const mixed = this.mixString(base64, mixKey);
            
            const checksum = this.calculateChecksum(base64);
            
            return {
                data: mixed,
                checksum: checksum,
                timestamp: Date.now(),
                version: '2.0'
            };
        } catch (error) {
            throw new Error('Encryption failed: ' + error.message);
        }
    }

    encryptCode(codeString, password) {
        const encryptedData = this.encrypt({ code: codeString }, password);
        return encryptedData;
    }
}

// 主执行函数
function main() {
    try {
        console.log('开始加密核心代码...');
        
        // 读取原始核心代码
        const originalCode = fs.readFileSync('content.original.js', 'utf8');
        
        // 提取核心功能代码（从handleData开始到文件结束，但排除最后的init()调用）
        const coreCodeStart = originalCode.indexOf('const handleData = (data) => {');
        const coreCodeEnd = originalCode.lastIndexOf('init();');
        
        if (coreCodeStart === -1) {
            throw new Error('找不到核心代码起始点');
        }
        
        const coreCode = originalCode.substring(coreCodeStart, coreCodeEnd).trim();
        
        console.log('提取的核心代码长度:', coreCode.length);
        console.log('核心代码预览:', coreCode.substring(0, 100) + '...');
        
        // 加密核心代码
        const crypto = new EnhancedCryptoUtils();
        const encryptedData = crypto.encryptCode(coreCode, 'simba');
        
        // 保存加密后的代码
        fs.writeFileSync('content.encrypted.json', JSON.stringify(encryptedData, null, 2));
        
        console.log('✅ 核心代码加密成功！');
        console.log('加密数据大小:', JSON.stringify(encryptedData).length);
        console.log('校验和:', encryptedData.checksum);
        console.log('时间戳:', new Date(encryptedData.timestamp).toISOString());
        
        // 验证加密结果
        console.log('\n验证解密...');
        const decryptedCode = crypto.decryptCode(encryptedData, 'simba');
        console.log('解密成功，代码长度:', decryptedCode.length);
        console.log('解密代码预览:', decryptedCode.substring(0, 100) + '...');
        
    } catch (error) {
        console.error('❌ 加密失败:', error.message);
        console.error('错误详情:', error.stack);
    }
}

// 运行主函数
main();
