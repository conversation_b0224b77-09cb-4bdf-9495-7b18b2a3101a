// X User Info Enhancer - 加密保护启动器
// 需要密码验证才能启动核心功能

console.log('X User Info Enhancer: 加密保护版本已加载');

// 加密工具类（内嵌版本）
class EnhancedCryptoUtils {
    constructor() {
        this.mixChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=!@#$%^&*()_+-=[]{}|;:,.<>?~`';
        this.salt = 'X-Enhancer-Simba-2024';
        this.expectedPasswordHash = this.generatePasswordHash('simba');
    }

    generatePasswordHash(password) {
        const combined = password + this.salt;
        let hash = 0;
        for (let i = 0; i < combined.length; i++) {
            const char = combined.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16);
    }

    verifyPassword(password) {
        const inputHash = this.generatePasswordHash(password);
        return inputHash === this.expectedPasswordHash;
    }

    generateMixKey(password) {
        const hash = this.generatePasswordHash(password);
        let mixKey = '';
        for (let i = 0; i < hash.length; i++) {
            const charCode = hash.charCodeAt(i);
            mixKey += this.mixChars[charCode % this.mixChars.length];
        }
        return mixKey;
    }

    extractString(mixedString, mixKey) {
        let extracted = '';
        let keyIndex = 0;

        for (let i = 0; i < mixedString.length; i++) {
            const char = mixedString[i];

            if ((i + 1) % 4 === 0 && keyIndex < mixKey.length) {
                if (char === mixKey[keyIndex % mixKey.length]) {
                    keyIndex++;
                    continue;
                }
            }

            if (/[A-Za-z0-9+/=]/.test(char)) {
                extracted += char;
            }
        }

        return extracted;
    }

    calculateChecksum(data) {
        let checksum = 0;
        for (let i = 0; i < data.length; i++) {
            checksum += data.charCodeAt(i);
        }
        return (checksum * 31 + data.length).toString(16);
    }

    decrypt(encryptedData, password) {
        if (!this.verifyPassword(password)) {
            throw new Error('Invalid password');
        }

        try {
            const mixKey = this.generateMixKey(password);
            const extracted = this.extractString(encryptedData.data, mixKey);

            const calculatedChecksum = this.calculateChecksum(extracted);
            if (calculatedChecksum !== encryptedData.checksum) {
                throw new Error('Data integrity check failed');
            }

            const decoded = decodeURIComponent(escape(atob(extracted)));
            return JSON.parse(decoded);
        } catch (error) {
            throw new Error('Decryption failed: ' + error.message);
        }
    }

    decryptCode(encryptedData, password) {
        const decryptedData = this.decrypt(encryptedData, password);
        return decryptedData.code;
    }
}

// 全局变量
let isAuthenticated = false;
let coreCode = null;
const crypto = new EnhancedCryptoUtils();

// 初始化函数
const init = () => {
    console.log('X User Info Enhancer: 等待密码验证...');

    // 检查是否已经验证过密码
    chrome.storage.local.get(['isAuthenticated', 'sessionToken'], (data) => {
        if (data.isAuthenticated && data.sessionToken) {
            // 验证会话令牌
            const sessionValid = validateSession(data.sessionToken);
            if (sessionValid) {
                console.log('X User Info Enhancer: 会话有效，尝试启动...');
                loadAndStartCore();
                return;
            }
        }

        console.log('X User Info Enhancer: 需要密码验证');
        // 监听来自popup的密码验证消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'VERIFY_PASSWORD') {
                handlePasswordVerification(message.password, sendResponse);
                return true; // 保持消息通道开放
            }
        });
    });
};

// 验证会话令牌
const validateSession = (sessionToken) => {
    try {
        const tokenData = JSON.parse(atob(sessionToken));
        const now = Date.now();
        // 会话有效期24小时
        return (now - tokenData.timestamp) < (24 * 60 * 60 * 1000);
    } catch (error) {
        return false;
    }
};

// 处理密码验证
const handlePasswordVerification = (password, sendResponse) => {
    try {
        if (crypto.verifyPassword(password)) {
            console.log('X User Info Enhancer: 密码验证成功');

            // 生成会话令牌
            const sessionToken = btoa(JSON.stringify({
                timestamp: Date.now(),
                hash: crypto.generatePasswordHash(password)
            }));

            // 保存认证状态
            chrome.storage.local.set({
                isAuthenticated: true,
                sessionToken: sessionToken
            });

            isAuthenticated = true;
            loadAndStartCore();
            sendResponse({ success: true, message: '密码验证成功' });
        } else {
            console.log('X User Info Enhancer: 密码验证失败');
            sendResponse({ success: false, message: '密码错误' });
        }
    } catch (error) {
        console.error('X User Info Enhancer: 密码验证出错:', error);
        sendResponse({ success: false, message: '验证过程出错' });
    }
};

// 加载并启动核心代码
const loadAndStartCore = async () => {
    try {
        console.log('X User Info Enhancer: 正在加载加密的核心代码...');

        // 获取加密的核心代码
        const response = await fetch(chrome.runtime.getURL('content.encrypted.json'));
        const encryptedData = await response.json();

        console.log('X User Info Enhancer: 正在解密核心代码...');

        // 解密核心代码
        coreCode = crypto.decryptCode(encryptedData, 'simba');

        console.log('X User Info Enhancer: 核心代码解密成功，正在启动...');

        // 执行核心代码
        executeCore();

    } catch (error) {
        console.error('X User Info Enhancer: 加载核心代码失败:', error);
        // 清除认证状态
        chrome.storage.local.remove(['isAuthenticated', 'sessionToken']);
        isAuthenticated = false;
    }
};

// 执行核心代码
const executeCore = () => {
    try {
        // 创建一个安全的执行环境
        const coreFunction = new Function('chrome', 'console', 'document', 'window', coreCode);
        coreFunction(chrome, console, document, window);
        console.log('X User Info Enhancer: 核心功能已启动');
    } catch (error) {
        console.error('X User Info Enhancer: 执行核心代码失败:', error);
    }
};

// 清除认证状态的函数
const clearAuthentication = () => {
    chrome.storage.local.remove(['isAuthenticated', 'sessionToken']);
    isAuthenticated = false;
    coreCode = null;
    console.log('X User Info Enhancer: 认证状态已清除');
};

// 监听来自popup的清除认证消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'CLEAR_AUTH') {
        clearAuthentication();
        sendResponse({ success: true });
    }
});

// 启动初始化
init();


