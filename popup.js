document.addEventListener('DOMContentLoaded', () => {
  const authSection = document.getElementById('authSection');
  const controlSection = document.getElementById('controlSection');
  const passwordInput = document.getElementById('passwordInput');
  const authButton = document.getElementById('authButton');
  const authMessage = document.getElementById('authMessage');
  const enabledToggle = document.getElementById('enabledToggle');

  // 初始化加密工具
  const crypto = new CryptoUtils();

  // 检查是否已经验证过
  chrome.storage.local.get(['sessionToken', 'isEnabled'], (data) => {
    if (data.sessionToken && crypto.verifySessionToken(data.sessionToken)) {
      // 已验证，显示控制面板
      showControlPanel();
      enabledToggle.checked = !!data.isEnabled;
    } else {
      // 未验证，显示密码输入
      showAuthPanel();
    }
  });

  // 密码验证
  authButton.addEventListener('click', handleAuth);
  passwordInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      handleAuth();
    }
  });

  // 插件开关
  enabledToggle.addEventListener('change', () => {
    chrome.storage.local.set({ isEnabled: enabledToggle.checked });
  });

  function handleAuth() {
    const password = passwordInput.value.trim();

    if (!password) {
      showMessage('请输入密码', 'error');
      return;
    }

    if (crypto.verifyPassword(password)) {
      // 验证成功
      const sessionToken = crypto.generateSessionToken();
      chrome.storage.local.set({
        sessionToken: sessionToken,
        isAuthenticated: true
      }, () => {
        showMessage('验证成功！', 'success');
        setTimeout(() => {
          showControlPanel();
          // 加载插件状态
          chrome.storage.local.get('isEnabled', (data) => {
            enabledToggle.checked = !!data.isEnabled;
          });
        }, 1000);
      });
    } else {
      // 验证失败
      showMessage('密码错误，请重试', 'error');
      passwordInput.value = '';
      passwordInput.focus();
    }
  }

  function showAuthPanel() {
    authSection.classList.remove('hidden');
    controlSection.classList.add('hidden');
    passwordInput.focus();
  }

  function showControlPanel() {
    authSection.classList.add('hidden');
    controlSection.classList.remove('hidden');
  }

  function showMessage(text, type) {
    authMessage.textContent = text;
    authMessage.className = type === 'error' ? 'error-message' : 'success-message';

    // 清除之前的定时器
    if (window.messageTimeout) {
      clearTimeout(window.messageTimeout);
    }

    // 3秒后清除消息
    window.messageTimeout = setTimeout(() => {
      authMessage.textContent = '';
      authMessage.className = '';
    }, 3000);
  }
});
