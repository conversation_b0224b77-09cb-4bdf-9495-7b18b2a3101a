/**
 * 加密工具模块
 * 使用混合字符的base64编码来避免直接的base64反向破解
 */

class CryptoUtils {
  constructor() {
    // 混合字符串 - 用于增加破解难度
    this.mixString = "XuSeRiNfOeNhAnCeR2024!@#$%^&*()_+-=[]{}|;:,.<>?";
    // 预设的加密密码 (simba)
    this.encryptedPassword = this.encryptPassword("simba");
  }

  /**
   * 将字符串与混合字符交替组合
   * @param {string} str - 要处理的字符串
   * @returns {string} - 混合后的字符串
   */
  mixWithChars(str) {
    let result = '';
    const mixLen = this.mixString.length;
    
    for (let i = 0; i < str.length; i++) {
      result += str[i];
      // 每个字符后添加一个混合字符
      if (i < str.length - 1) {
        result += this.mixString[i % mixLen];
      }
    }
    return result;
  }

  /**
   * 从混合字符串中提取原始字符串
   * @param {string} mixedStr - 混合后的字符串
   * @returns {string} - 原始字符串
   */
  unmixChars(mixedStr) {
    let result = '';
    for (let i = 0; i < mixedStr.length; i += 2) {
      result += mixedStr[i];
    }
    return result;
  }

  /**
   * 加密密码
   * @param {string} password - 原始密码
   * @returns {string} - 加密后的密码
   */
  encryptPassword(password) {
    // 1. 先进行简单的字符位移
    let shifted = '';
    for (let i = 0; i < password.length; i++) {
      shifted += String.fromCharCode(password.charCodeAt(i) + 3);
    }
    
    // 2. 与混合字符交替
    const mixed = this.mixWithChars(shifted);
    
    // 3. Base64编码
    const encoded = btoa(mixed);
    
    // 4. 再次与混合字符交替
    const finalMixed = this.mixWithChars(encoded);
    
    return finalMixed;
  }

  /**
   * 解密密码
   * @param {string} encryptedPassword - 加密的密码
   * @returns {string} - 解密后的密码
   */
  decryptPassword(encryptedPassword) {
    try {
      // 1. 提取base64部分
      const base64Part = this.unmixChars(encryptedPassword);
      
      // 2. Base64解码
      const decoded = atob(base64Part);
      
      // 3. 提取位移后的字符串
      const shifted = this.unmixChars(decoded);
      
      // 4. 字符位移还原
      let original = '';
      for (let i = 0; i < shifted.length; i++) {
        original += String.fromCharCode(shifted.charCodeAt(i) - 3);
      }
      
      return original;
    } catch (error) {
      return null;
    }
  }

  /**
   * 验证密码
   * @param {string} inputPassword - 用户输入的密码
   * @returns {boolean} - 验证结果
   */
  verifyPassword(inputPassword) {
    const decrypted = this.decryptPassword(this.encryptedPassword);
    return inputPassword === decrypted;
  }

  /**
   * 生成会话令牌
   * @returns {string} - 会话令牌
   */
  generateSessionToken() {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2);
    const combined = timestamp + random;
    return this.encryptPassword(combined);
  }

  /**
   * 验证会话令牌是否有效（24小时内）
   * @param {string} token - 会话令牌
   * @returns {boolean} - 令牌是否有效
   */
  verifySessionToken(token) {
    try {
      const decrypted = this.decryptPassword(token);
      if (!decrypted) return false;
      
      const timestamp = parseInt(decrypted.substring(0, 13));
      const now = Date.now();
      const twentyFourHours = 24 * 60 * 60 * 1000;
      
      return (now - timestamp) < twentyFourHours;
    } catch (error) {
      return false;
    }
  }
}

// 导出给其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CryptoUtils;
} else {
  window.CryptoUtils = CryptoUtils;
}
