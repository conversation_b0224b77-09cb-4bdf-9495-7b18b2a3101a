<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>X User Info Enhancer</title>
  <style>
    body {
      font-family: sans-serif;
      width: 250px;
      padding: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px;
    }
    .container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
    }
    .auth-container {
      text-align: center;
      padding: 20px 0;
    }
    .auth-input {
      width: 100%;
      padding: 8px 12px;
      border: none;
      border-radius: 4px;
      margin: 10px 0;
      font-size: 14px;
      box-sizing: border-box;
    }
    .auth-button {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      width: 100%;
      margin-top: 10px;
    }
    .auth-button:hover {
      background: #45a049;
    }
    .error-message {
      color: #ffcccb;
      font-size: 12px;
      margin-top: 5px;
    }
    .success-message {
      color: #90EE90;
      font-size: 12px;
      margin-top: 5px;
    }
    .hidden {
      display: none;
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #2196F3;
    }
    input:checked + .slider:before {
      transform: translateX(26px);
    }
  </style>
</head>
<body>
  <div id="authSection" class="auth-container">
    <h3>🔐 插件验证</h3>
    <p>请输入密码以启用插件</p>
    <input type="password" id="passwordInput" class="auth-input" placeholder="输入密码...">
    <button id="authButton" class="auth-button">验证</button>
    <div id="authMessage"></div>
  </div>

  <div id="controlSection" class="container hidden">
    <span>启用增强器</span>
    <label class="switch">
      <input type="checkbox" id="enabledToggle">
      <span class="slider"></span>
    </label>
  </div>

  <script src="crypto-utils.js"></script>
  <script src="popup.js"></script>
</body>
</html>
