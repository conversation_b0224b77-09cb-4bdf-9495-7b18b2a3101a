// 增强加密工具库 - 基于simba密码的混合加密
class EnhancedCryptoUtils {
    constructor() {
        // 混合字符池 - 用于干扰base64解码
        this.mixChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=!@#$%^&*()_+-=[]{}|;:,.<>?~`';
        this.salt = 'X-Enhancer-Simba-2024';
        this.expectedPasswordHash = this.generatePasswordHash('simba');
    }

    // 生成密码哈希
    generatePasswordHash(password) {
        const combined = password + this.salt;
        let hash = 0;
        for (let i = 0; i < combined.length; i++) {
            const char = combined.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16);
    }

    // 验证密码
    verifyPassword(password) {
        const inputHash = this.generatePasswordHash(password);
        return inputHash === this.expectedPasswordHash;
    }

    // 生成混合密钥
    generateMixKey(password) {
        const hash = this.generatePasswordHash(password);
        let mixKey = '';
        for (let i = 0; i < hash.length; i++) {
            const charCode = hash.charCodeAt(i);
            mixKey += this.mixChars[charCode % this.mixChars.length];
        }
        return mixKey;
    }

    // 混合字符串 - 在base64编码中插入干扰字符
    mixString(base64String, mixKey) {
        let mixed = '';
        let keyIndex = 0;
        
        for (let i = 0; i < base64String.length; i++) {
            mixed += base64String[i];
            
            // 每隔3个字符插入一个混合字符
            if ((i + 1) % 3 === 0 && keyIndex < mixKey.length) {
                mixed += mixKey[keyIndex % mixKey.length];
                keyIndex++;
            }
        }
        
        // 在末尾添加更多混合字符
        const extraMixCount = mixKey.length % 5 + 2;
        for (let i = 0; i < extraMixCount; i++) {
            mixed += mixKey[i % mixKey.length];
        }
        
        return mixed;
    }

    // 提取原始字符串 - 从混合字符串中提取base64
    extractString(mixedString, mixKey) {
        let extracted = '';
        let keyIndex = 0;
        
        for (let i = 0; i < mixedString.length; i++) {
            const char = mixedString[i];
            
            // 检查是否是混合字符
            if ((i + 1) % 4 === 0 && keyIndex < mixKey.length) {
                // 跳过混合字符
                if (char === mixKey[keyIndex % mixKey.length]) {
                    keyIndex++;
                    continue;
                }
            }
            
            // 检查是否是有效的base64字符
            if (/[A-Za-z0-9+/=]/.test(char)) {
                extracted += char;
            }
        }
        
        return extracted;
    }

    // 加密数据
    encrypt(data, password) {
        if (!this.verifyPassword(password)) {
            throw new Error('Invalid password');
        }

        try {
            const jsonString = JSON.stringify(data);
            const base64 = btoa(unescape(encodeURIComponent(jsonString)));
            const mixKey = this.generateMixKey(password);
            const mixed = this.mixString(base64, mixKey);
            
            // 计算校验和
            const checksum = this.calculateChecksum(base64);
            
            return {
                data: mixed,
                checksum: checksum,
                timestamp: Date.now(),
                version: '2.0'
            };
        } catch (error) {
            throw new Error('Encryption failed: ' + error.message);
        }
    }

    // 解密数据
    decrypt(encryptedData, password) {
        if (!this.verifyPassword(password)) {
            throw new Error('Invalid password');
        }

        try {
            const mixKey = this.generateMixKey(password);
            const extracted = this.extractString(encryptedData.data, mixKey);
            
            // 验证校验和
            const calculatedChecksum = this.calculateChecksum(extracted);
            if (calculatedChecksum !== encryptedData.checksum) {
                throw new Error('Data integrity check failed');
            }
            
            const decoded = decodeURIComponent(escape(atob(extracted)));
            return JSON.parse(decoded);
        } catch (error) {
            throw new Error('Decryption failed: ' + error.message);
        }
    }

    // 计算校验和
    calculateChecksum(data) {
        let checksum = 0;
        for (let i = 0; i < data.length; i++) {
            checksum += data.charCodeAt(i);
        }
        return (checksum * 31 + data.length).toString(16);
    }

    // 生成加密的代码文件
    encryptCode(codeString, password) {
        const encryptedData = this.encrypt({ code: codeString }, password);
        return encryptedData;
    }

    // 解密代码文件
    decryptCode(encryptedData, password) {
        const decryptedData = this.decrypt(encryptedData, password);
        return decryptedData.code;
    }
}

// 导出工具类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedCryptoUtils;
} else {
    window.EnhancedCryptoUtils = EnhancedCryptoUtils;
}
