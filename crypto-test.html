<!DOCTYPE html>
<html>
<head>
    <title>加密功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        input, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 X User Info Enhancer - 加密功能测试</h1>
        
        <div class="test-section">
            <h3>1. 密码加密测试</h3>
            <p>测试密码 "simba" 的加密和解密过程</p>
            <button onclick="testPasswordEncryption()">运行测试</button>
            <div id="encryptionResult"></div>
        </div>

        <div class="test-section">
            <h3>2. 密码验证测试</h3>
            <input type="text" id="testPassword" placeholder="输入密码进行验证" value="simba">
            <button onclick="testPasswordVerification()">验证密码</button>
            <div id="verificationResult"></div>
        </div>

        <div class="test-section">
            <h3>3. 会话令牌测试</h3>
            <button onclick="testSessionToken()">生成并验证会话令牌</button>
            <div id="sessionResult"></div>
        </div>

        <div class="test-section">
            <h3>4. 破解难度演示</h3>
            <p>展示混合字符如何增加破解难度</p>
            <button onclick="demonstrateSecurity()">演示安全性</button>
            <div id="securityDemo"></div>
        </div>
    </div>

    <script src="crypto-utils.js"></script>
    <script>
        const crypto = new CryptoUtils();

        function testPasswordEncryption() {
            const result = document.getElementById('encryptionResult');
            const password = "simba";
            
            try {
                const encrypted = crypto.encryptPassword(password);
                const decrypted = crypto.decryptPassword(encrypted);
                
                result.innerHTML = `
                    <div class="result">
                        <strong>原始密码:</strong> ${password}<br>
                        <strong>加密后:</strong> ${encrypted}<br>
                        <strong>解密后:</strong> ${decrypted}<br>
                        <strong>测试结果:</strong> <span class="${password === decrypted ? 'success' : 'error'}">
                            ${password === decrypted ? '✅ 成功' : '❌ 失败'}
                        </span>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="result error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        function testPasswordVerification() {
            const result = document.getElementById('verificationResult');
            const inputPassword = document.getElementById('testPassword').value;
            
            try {
                const isValid = crypto.verifyPassword(inputPassword);
                
                result.innerHTML = `
                    <div class="result">
                        <strong>输入密码:</strong> ${inputPassword}<br>
                        <strong>验证结果:</strong> <span class="${isValid ? 'success' : 'error'}">
                            ${isValid ? '✅ 密码正确' : '❌ 密码错误'}
                        </span>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="result error">❌ 验证失败: ${error.message}</div>`;
            }
        }

        function testSessionToken() {
            const result = document.getElementById('sessionResult');
            
            try {
                const token = crypto.generateSessionToken();
                const isValid = crypto.verifySessionToken(token);
                
                result.innerHTML = `
                    <div class="result">
                        <strong>生成的令牌:</strong> ${token.substring(0, 50)}...<br>
                        <strong>令牌长度:</strong> ${token.length} 字符<br>
                        <strong>验证结果:</strong> <span class="${isValid ? 'success' : 'error'}">
                            ${isValid ? '✅ 令牌有效' : '❌ 令牌无效'}
                        </span>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="result error">❌ 令牌测试失败: ${error.message}</div>`;
            }
        }

        function demonstrateSecurity() {
            const result = document.getElementById('securityDemo');
            const password = "simba";
            
            try {
                // 显示不同加密步骤
                let shifted = '';
                for (let i = 0; i < password.length; i++) {
                    shifted += String.fromCharCode(password.charCodeAt(i) + 3);
                }
                
                const mixed1 = crypto.mixWithChars(shifted);
                const base64 = btoa(mixed1);
                const final = crypto.mixWithChars(base64);
                
                // 尝试简单的base64解码
                let simpleBase64;
                try {
                    simpleBase64 = atob(final);
                } catch (e) {
                    simpleBase64 = "❌ 无法直接解码";
                }
                
                result.innerHTML = `
                    <div class="result">
                        <strong>1. 原始密码:</strong> ${password}<br>
                        <strong>2. 字符位移:</strong> ${shifted}<br>
                        <strong>3. 第一次混合:</strong> ${mixed1}<br>
                        <strong>4. Base64编码:</strong> ${base64}<br>
                        <strong>5. 最终加密:</strong> ${final}<br>
                        <strong>6. 直接Base64解码尝试:</strong> ${simpleBase64}<br>
                        <br>
                        <span class="success">✅ 混合字符成功阻止了直接的Base64反向破解</span>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="result error">❌ 安全演示失败: ${error.message}</div>`;
            }
        }

        // 页面加载时运行基本测试
        window.onload = function() {
            testPasswordEncryption();
        };
    </script>
</body>
</html>
