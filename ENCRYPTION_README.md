# X User Info Enhancer - 加密功能说明

## 🔐 功能概述

本插件已集成基础加密功能，用户需要输入正确的密码 `simba` 才能启用插件功能。加密采用混合字符的base64编码方式，有效防止直接的base64反向破解。

## 🛡️ 安全特性

### 1. 多层加密机制
- **字符位移**: 对原始密码进行ASCII码位移（+3）
- **混合字符**: 与预设字符串交替混合，增加破解难度
- **Base64编码**: 标准base64编码
- **二次混合**: 对base64结果再次混合字符

### 2. 会话管理
- 验证成功后生成24小时有效的会话令牌
- 令牌包含时间戳和随机数，确保唯一性
- 自动过期机制，提高安全性

### 3. 防破解设计
- 混合字符串: `XuSeRiNfOeNhAnCeR2024!@#$%^&*()_+-=[]{}|;:,.<>?`
- 无法通过简单的base64解码获取原始信息
- 需要知道完整的解密算法才能破解

## 📁 文件结构

```
├── crypto-utils.js      # 加密工具类
├── popup.html          # 更新的弹窗界面（包含密码验证）
├── popup.js            # 更新的弹窗逻辑
├── content.js          # 更新的内容脚本（包含验证检查）
├── crypto-test.html    # 加密功能测试页面
└── ENCRYPTION_README.md # 本说明文件
```

## 🔧 使用方法

### 1. 安装插件
1. 在Chrome浏览器中打开 `chrome://extensions/`
2. 启用"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择插件目录

### 2. 验证密码
1. 点击插件图标打开弹窗
2. 输入密码: `simba`
3. 点击"验证"按钮
4. 验证成功后显示插件控制面板

### 3. 使用插件
- 验证成功后，插件将在X/Twitter页面上正常工作
- 会话有效期为24小时
- 可以通过开关控制插件的启用/禁用

## 🧪 测试功能

打开 `crypto-test.html` 文件可以测试加密功能：

1. **密码加密测试**: 验证加密和解密过程
2. **密码验证测试**: 测试不同密码的验证结果
3. **会话令牌测试**: 验证令牌生成和验证机制
4. **安全性演示**: 展示混合字符如何防止简单破解

## 🔒 加密算法详解

### 加密过程
```
原始密码 "simba"
    ↓ 字符位移 (+3)
"vlped"
    ↓ 混合字符
"vXlupSeRdiNfO..."
    ↓ Base64编码
"dlh1cFNlUmRpTmZP..."
    ↓ 再次混合字符
"dXluhS1ecRFdpTmZP..."
```

### 解密过程
```
加密字符串
    ↓ 提取base64部分
    ↓ Base64解码
    ↓ 提取原始字符
    ↓ 字符位移还原 (-3)
原始密码
```

## ⚙️ 自定义配置

### 修改密码
在 `crypto-utils.js` 中修改：
```javascript
// 将 "simba" 替换为您的密码
this.encryptedPassword = this.encryptPassword("your_new_password");
```

### 修改混合字符
在 `crypto-utils.js` 中修改：
```javascript
// 自定义混合字符串
this.mixString = "your_custom_mix_string";
```

### 修改会话有效期
在 `verifySessionToken` 方法中修改：
```javascript
// 修改有效期（毫秒）
const twentyFourHours = 24 * 60 * 60 * 1000; // 24小时
```

## 🚀 技术特点

1. **轻量级**: 加密逻辑简单高效，不依赖外部库
2. **兼容性**: 使用标准JavaScript API，兼容所有现代浏览器
3. **安全性**: 多层加密，有效防止常见的破解尝试
4. **用户友好**: 简洁的验证界面，良好的用户体验
5. **可扩展**: 易于修改密码和加密参数

## ⚠️ 注意事项

1. 这是基础级别的加密，主要用于防止普通用户的简单破解
2. 对于高安全性要求的场景，建议使用更复杂的加密算法
3. 密码硬编码在代码中，适合个人使用或内部分发
4. 会话令牌存储在本地，清除浏览器数据会导致需要重新验证

## 🔄 更新日志

- **v1.1**: 添加基础加密功能
  - 密码验证机制
  - 会话管理
  - 混合字符防破解
  - 测试页面
